0
53
__consumer_offsets 29 0
__consumer_offsets 43 0
__consumer_offsets 0 0
__consumer_offsets 6 0
__consumer_offsets 35 0
__consumer_offsets 30 0
__consumer_offsets 13 0
__consumer_offsets 26 0
__consumer_offsets 21 0
__consumer_offsets 19 0
__consumer_offsets 25 0
__consumer_offsets 33 0
__consumer_offsets 41 0
balte_oms_test_db_executor_mcx 0 0
__consumer_offsets 37 0
__consumer_offsets 8 0
__consumer_offsets 24 0
__consumer_offsets 49 0
__consumer_offsets 3 0
__consumer_offsets 40 0
__consumer_offsets 27 0
__consumer_offsets 17 0
__consumer_offsets 32 0
balte_test_engines_mcx 0 0
__consumer_offsets 39 0
__consumer_offsets 2 1
__consumer_offsets 12 0
__consumer_offsets 44 0
__consumer_offsets 36 0
__consumer_offsets 45 0
__consumer_offsets 16 0
__consumer_offsets 10 0
__consumer_offsets 11 0
__consumer_offsets 20 0
__consumer_offsets 47 0
__consumer_offsets 18 0
balte_oms_test_logs_mcx 0 0
__consumer_offsets 7 0
__consumer_offsets 48 0
__consumer_offsets 22 0
__consumer_offsets 46 0
__consumer_offsets 23 0
__consumer_offsets 42 0
__consumer_offsets 28 0
__consumer_offsets 4 0
__consumer_offsets 31 0
__consumer_offsets 5 0
__consumer_offsets 1 0
__consumer_offsets 15 0
__consumer_offsets 38 0
__consumer_offsets 34 0
__consumer_offsets 9 0
__consumer_offsets 14 0
