[2025-06-25 09:23:06,892] {manager.py:504} INFO - Processing files using up to 2 processes at a time 
[2025-06-25 09:23:06,893] {manager.py:505} INFO - Process each file at most once every 30 seconds
[2025-06-25 09:23:06,893] {manager.py:506} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-06-25 09:23:06,893] {manager.py:653} INFO - Searching for files in /opt/airflow/dags
[2025-06-25 09:23:06,895] {manager.py:656} INFO - There are 9 files in /opt/airflow/dags
[2025-06-25 09:23:06,926] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:06,926] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:06.926818+00:00
[2025-06-25 09:23:06,952] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  ----------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  0           0
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                0           0
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           0           0
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    0           0
/opt/airflow/dags/ops/dead_strat_exit_builder.py                   328  0.03s             0           0
/opt/airflow/dags/ops/oms_rollover_builder.py                                             0           0
/opt/airflow/dags/ops/one_second_exit_application_builder.py       331  0.03s             0           0
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              0           0
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        0           0
================================================================================
[2025-06-25 09:23:17,501] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:17,502] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:17.502130+00:00
[2025-06-25 09:23:27,924] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:27,924] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:27.924881+00:00
[2025-06-25 09:23:37,324] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  3.56s           2025-06-25T03:53:14
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  2.72s           2025-06-25T03:53:18
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.72s           2025-06-25T03:53:15
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.61s           2025-06-25T03:53:17
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.68s           2025-06-25T03:53:09
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.75s           2025-06-25T03:53:10
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.86s           2025-06-25T03:53:08
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.32s           2025-06-25T03:53:12
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.73s           2025-06-25T03:53:11
================================================================================
[2025-06-25 09:23:37,951] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:37,951] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:37.951441+00:00
[2025-06-25 09:23:47,967] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:47,968] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:47.968148+00:00
[2025-06-25 09:23:58,172] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:58,173] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:58.173349+00:00
[2025-06-25 09:24:07,797] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.49s           2025-06-25T03:53:47
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.82s           2025-06-25T03:53:50
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.38s           2025-06-25T03:53:48
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.78s           2025-06-25T03:53:50
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.12s           2025-06-25T03:53:42
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.64s           2025-06-25T03:53:42
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.85s           2025-06-25T03:53:40
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.87s           2025-06-25T03:53:44
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.86s           2025-06-25T03:53:43
================================================================================
[2025-06-25 09:24:08,204] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:08,204] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:08.204882+00:00
[2025-06-25 09:24:18,223] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:18,223] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:18.223707+00:00
[2025-06-25 09:24:28,280] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:28,280] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:28.280941+00:00
[2025-06-25 09:24:37,934] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  3.32s           2025-06-25T03:54:21
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.76s           2025-06-25T03:54:22
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.33s           2025-06-25T03:54:21
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.70s           2025-06-25T03:54:24
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.33s           2025-06-25T03:54:14
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.82s           2025-06-25T03:54:14
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.78s           2025-06-25T03:54:12
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.71s           2025-06-25T03:54:18
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.66s           2025-06-25T03:54:16
================================================================================
[2025-06-25 09:24:38,310] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:38,310] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:38.310798+00:00
[2025-06-25 09:24:48,473] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:48,474] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:48.474020+00:00
[2025-06-25 09:24:59,281] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:59,282] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:59.282142+00:00
[2025-06-25 09:25:08,316] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.32s           2025-06-25T03:54:53
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.75s           2025-06-25T03:54:55
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.24s           2025-06-25T03:54:53
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.79s           2025-06-25T03:54:57
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.52s           2025-06-25T03:54:47
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.82s           2025-06-25T03:54:46
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.80s           2025-06-25T03:54:45
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.71s           2025-06-25T03:54:50
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.75s           2025-06-25T03:54:48
================================================================================
[2025-06-25 09:25:09,318] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:09,318] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:09.318801+00:00
[2025-06-25 09:25:19,357] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:19,357] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:19.357797+00:00
[2025-06-25 09:25:29,678] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:29,679] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:29.679419+00:00
[2025-06-25 09:25:39,159] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.50s           2025-06-25T03:55:26
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.75s           2025-06-25T03:55:28
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.21s           2025-06-25T03:55:26
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.79s           2025-06-25T03:55:30
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.11s           2025-06-25T03:55:20
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.85s           2025-06-25T03:55:18
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.69s           2025-06-25T03:55:17
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.64s           2025-06-25T03:55:22
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.75s           2025-06-25T03:55:20
================================================================================
[2025-06-25 09:25:40,161] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:40,161] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:40.161562+00:00
[2025-06-25 09:25:50,214] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:50,215] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:50.215078+00:00
[2025-06-25 09:26:00,632] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:00,632] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:00.632480+00:00
[2025-06-25 09:26:09,395] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.34s           2025-06-25T03:55:59
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.75s           2025-06-25T03:56:01
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  3.27s           2025-06-25T03:55:59
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.79s           2025-06-25T03:56:03
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.26s           2025-06-25T03:55:52
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.73s           2025-06-25T03:55:50
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.80s           2025-06-25T03:55:48
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.80s           2025-06-25T03:55:54
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.75s           2025-06-25T03:55:52
================================================================================
[2025-06-25 09:26:11,399] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:11,399] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:11.399747+00:00
[2025-06-25 09:26:21,462] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:21,462] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:21.462496+00:00
[2025-06-25 09:26:31,669] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:31,669] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:31.669849+00:00
[2025-06-25 09:26:39,496] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.26s           2025-06-25T03:56:31
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.73s           2025-06-25T03:56:33
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.20s           2025-06-25T03:56:32
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.84s           2025-06-25T03:56:36
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.18s           2025-06-25T03:56:25
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.75s           2025-06-25T03:56:22
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.66s           2025-06-25T03:56:21
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.89s           2025-06-25T03:56:26
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.92s           2025-06-25T03:56:24
================================================================================
[2025-06-25 09:26:42,256] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:42,256] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:42.256429+00:00
[2025-06-25 09:26:52,292] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:52,292] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:52.292770+00:00
[2025-06-25 09:27:02,797] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:02,798] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:02.798174+00:00
[2025-06-25 09:27:09,601] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.39s           2025-06-25T03:57:05
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.69s           2025-06-25T03:57:06
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.42s           2025-06-25T03:57:05
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.71s           2025-06-25T03:57:09
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.26s           2025-06-25T03:56:57
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.74s           2025-06-25T03:56:54
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.72s           2025-06-25T03:56:53
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.68s           2025-06-25T03:56:58
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.68s           2025-06-25T03:56:57
================================================================================
[2025-06-25 09:27:13,243] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:13,243] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:13.243256+00:00
[2025-06-25 09:27:23,274] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:23,275] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:23.275164+00:00
[2025-06-25 09:27:33,409] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:33,409] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:33.409495+00:00
[2025-06-25 09:27:39,866] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.35s           2025-06-25T03:57:37
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.66s           2025-06-25T03:57:39
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.42s           2025-06-25T03:57:37
/opt/airflow/dags/monitoring/trading_checks_builder.py            5014  0.01s             1           0  2.71s           2025-06-25T03:57:09
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  3.20s           2025-06-25T03:57:31
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.81s           2025-06-25T03:57:26
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.80s           2025-06-25T03:57:25
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.01s           2025-06-25T03:57:31
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.76s           2025-06-25T03:57:29
================================================================================
[2025-06-25 09:27:43,440] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:43,441] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:43.441124+00:00
[2025-06-25 09:27:53,476] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:53,477] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:53.476992+00:00
[2025-06-25 09:28:03,548] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:03,549] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:03.549037+00:00
[2025-06-25 09:28:07,373] {manager.py:653} INFO - Searching for files in /opt/airflow/dags
[2025-06-25 09:28:07,377] {manager.py:656} INFO - There are 9 files in /opt/airflow/dags
[2025-06-25 09:28:10,403] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py          5346  2.01s             1           0  2.35s           2025-06-25T03:57:37
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.66s           2025-06-25T03:57:39
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py   5349  2.01s             1           0  2.42s           2025-06-25T03:57:37
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.70s           2025-06-25T03:57:42
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.33s           2025-06-25T03:58:03
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.81s           2025-06-25T03:57:59
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.94s           2025-06-25T03:57:57
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.83s           2025-06-25T03:58:05
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  2.95s           2025-06-25T03:58:02
================================================================================
[2025-06-25 09:28:13,741] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:13,741] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:13.741532+00:00
[2025-06-25 09:28:24,283] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:24,283] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:24.283675+00:00
[2025-06-25 09:28:34,517] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:34,518] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:34.518278+00:00
[2025-06-25 09:28:40,973] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py          5875  0.01s             1           0  2.34s           2025-06-25T03:58:10
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.59s           2025-06-25T03:58:12
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py   5878  0.00s             1           0  2.28s           2025-06-25T03:58:10
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.73s           2025-06-25T03:58:15
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.29s           2025-06-25T03:58:36
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.60s           2025-06-25T03:58:31
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.71s           2025-06-25T03:58:29
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.70s           2025-06-25T03:58:37
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.85s           2025-06-25T03:58:34
================================================================================
[2025-06-25 09:28:44,965] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:44,965] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:44.965642+00:00
[2025-06-25 09:28:55,285] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:55,285] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:55.285436+00:00
[2025-06-25 09:29:05,485] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:05,486] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:05.486229+00:00
[2025-06-25 09:29:11,248] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.25s           2025-06-25T03:58:43
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.75s           2025-06-25T03:58:44
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.30s           2025-06-25T03:58:43
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.67s           2025-06-25T03:58:48
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.26s           2025-06-25T03:59:08
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.81s           2025-06-25T03:59:03
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.73s           2025-06-25T03:59:01
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.74s           2025-06-25T03:59:09
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  2.00s           2025-06-25T03:59:07
================================================================================
[2025-06-25 09:29:15,574] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:15,574] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:15.574331+00:00
[2025-06-25 09:29:25,763] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:25,763] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:25.763918+00:00
[2025-06-25 09:29:35,986] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:35,987] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:35.986992+00:00
[2025-06-25 09:29:41,398] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.32s           2025-06-25T03:59:15
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.64s           2025-06-25T03:59:17
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.09s           2025-06-25T03:59:15
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.89s           2025-06-25T03:59:21
/opt/airflow/dags/ops/dead_strat_exit_builder.py                  6810  2.39s             1           0  2.26s           2025-06-25T03:59:08
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.91s           2025-06-25T03:59:35
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.73s           2025-06-25T03:59:32
/opt/airflow/dags/ops/oms_db_copy_builder.py                      6855  2.01s             1           0  1.74s           2025-06-25T03:59:09
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.68s           2025-06-25T03:59:39
================================================================================
[2025-06-25 09:29:46,630] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:46,630] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:46.630627+00:00
[2025-06-25 09:29:57,224] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:57,224] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:57.224430+00:00
[2025-06-25 09:30:07,501] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:07,501] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:07.501382+00:00
[2025-06-25 09:30:11,432] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.20s           2025-06-25T03:59:47
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.64s           2025-06-25T03:59:49
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.26s           2025-06-25T03:59:48
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.97s           2025-06-25T03:59:55
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.61s           2025-06-25T03:59:41
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.67s           2025-06-25T04:00:07
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.92s           2025-06-25T04:00:05
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.59s           2025-06-25T03:59:41
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.91s           2025-06-25T04:00:11
================================================================================
[2025-06-25 09:30:18,295] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:18,295] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:18.295708+00:00
[2025-06-25 09:30:28,837] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:28,837] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:28.837796+00:00
[2025-06-25 09:30:39,174] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:39,174] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:39.174429+00:00
[2025-06-25 09:30:41,663] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.30s           2025-06-25T04:00:20
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.69s           2025-06-25T04:00:22
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.51s           2025-06-25T04:00:20
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.84s           2025-06-25T04:00:28
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.62s           2025-06-25T04:00:14
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.92s           2025-06-25T04:00:40
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.78s           2025-06-25T04:00:37
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.85s           2025-06-25T04:00:14
/opt/airflow/dags/ops/oms_latency_check_builder.py                7825  0.01s             1           0  1.91s           2025-06-25T04:00:11
================================================================================
[2025-06-25 09:30:49,346] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:49,347] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:49.347322+00:00
[2025-06-25 09:30:59,658] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:59,659] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:59.658997+00:00
[2025-06-25 09:31:10,184] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:10,184] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:10.184638+00:00
[2025-06-25 09:31:11,880] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.28s           2025-06-25T04:00:53
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.67s           2025-06-25T04:00:55
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.20s           2025-06-25T04:00:53
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.92s           2025-06-25T04:01:01
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.43s           2025-06-25T04:00:47
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.68s           2025-06-25T04:01:11
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.71s           2025-06-25T04:01:09
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.74s           2025-06-25T04:00:47
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.67s           2025-06-25T04:00:43
================================================================================
[2025-06-25 09:31:21,088] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:21,088] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:21.088379+00:00
[2025-06-25 09:31:31,112] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:31,112] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:31.112465+00:00
[2025-06-25 09:31:41,151] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:41,152] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:41.152075+00:00
[2025-06-25 09:31:42,118] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.43s           2025-06-25T04:01:26
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.64s           2025-06-25T04:01:28
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.35s           2025-06-25T04:01:26
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.70s           2025-06-25T04:01:34
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.19s           2025-06-25T04:01:20
/opt/airflow/dags/ops/oms_rollover_builder.py                     8838  0.00s             1           0  1.68s           2025-06-25T04:01:11
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.97s           2025-06-25T04:01:42
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.94s           2025-06-25T04:01:19
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.72s           2025-06-25T04:01:15
================================================================================
[2025-06-25 09:31:51,845] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:51,845] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:51.845528+00:00
[2025-06-25 09:32:02,294] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:02,295] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:02.295291+00:00
[2025-06-25 09:32:12,153] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.31s           2025-06-25T04:01:59
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.83s           2025-06-25T04:02:01
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.49s           2025-06-25T04:01:59
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  3.83s           2025-06-25T04:02:08
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.15s           2025-06-25T04:01:52
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.94s           2025-06-25T04:01:44
/opt/airflow/dags/ops/one_second_exit_application_builder.py      9322  0.01s             1           0  1.97s           2025-06-25T04:01:42
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.71s           2025-06-25T04:01:50
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.85s           2025-06-25T04:01:47
================================================================================
[2025-06-25 09:32:12,669] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:12,670] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:12.670004+00:00
[2025-06-25 09:32:23,071] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:23,071] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:23.071768+00:00
