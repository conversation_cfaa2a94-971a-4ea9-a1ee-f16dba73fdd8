[2025-06-25 09:23:06,892] {manager.py:504} INFO - Processing files using up to 2 processes at a time 
[2025-06-25 09:23:06,893] {manager.py:505} INFO - Process each file at most once every 30 seconds
[2025-06-25 09:23:06,893] {manager.py:506} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-06-25 09:23:06,893] {manager.py:653} INFO - Searching for files in /opt/airflow/dags
[2025-06-25 09:23:06,895] {manager.py:656} INFO - There are 9 files in /opt/airflow/dags
[2025-06-25 09:23:06,926] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:06,926] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:06.926818+00:00
[2025-06-25 09:23:06,952] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  ----------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  0           0
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                0           0
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           0           0
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    0           0
/opt/airflow/dags/ops/dead_strat_exit_builder.py                   328  0.03s             0           0
/opt/airflow/dags/ops/oms_rollover_builder.py                                             0           0
/opt/airflow/dags/ops/one_second_exit_application_builder.py       331  0.03s             0           0
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              0           0
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        0           0
================================================================================
[2025-06-25 09:23:17,501] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:17,502] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:17.502130+00:00
[2025-06-25 09:23:27,924] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:27,924] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:27.924881+00:00
[2025-06-25 09:23:37,324] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  3.56s           2025-06-25T03:53:14
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  2.72s           2025-06-25T03:53:18
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.72s           2025-06-25T03:53:15
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.61s           2025-06-25T03:53:17
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.68s           2025-06-25T03:53:09
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.75s           2025-06-25T03:53:10
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.86s           2025-06-25T03:53:08
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.32s           2025-06-25T03:53:12
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.73s           2025-06-25T03:53:11
================================================================================
[2025-06-25 09:23:37,951] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:37,951] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:37.951441+00:00
[2025-06-25 09:23:47,967] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:47,968] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:47.968148+00:00
[2025-06-25 09:23:58,172] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:23:58,173] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:48:58.173349+00:00
[2025-06-25 09:24:07,797] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.49s           2025-06-25T03:53:47
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.82s           2025-06-25T03:53:50
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.38s           2025-06-25T03:53:48
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.78s           2025-06-25T03:53:50
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.12s           2025-06-25T03:53:42
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.64s           2025-06-25T03:53:42
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.85s           2025-06-25T03:53:40
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.87s           2025-06-25T03:53:44
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.86s           2025-06-25T03:53:43
================================================================================
[2025-06-25 09:24:08,204] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:08,204] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:08.204882+00:00
[2025-06-25 09:24:18,223] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:18,223] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:18.223707+00:00
[2025-06-25 09:24:28,280] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:28,280] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:28.280941+00:00
[2025-06-25 09:24:37,934] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  3.32s           2025-06-25T03:54:21
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.76s           2025-06-25T03:54:22
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.33s           2025-06-25T03:54:21
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.70s           2025-06-25T03:54:24
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.33s           2025-06-25T03:54:14
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.82s           2025-06-25T03:54:14
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.78s           2025-06-25T03:54:12
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.71s           2025-06-25T03:54:18
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.66s           2025-06-25T03:54:16
================================================================================
[2025-06-25 09:24:38,310] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:38,310] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:38.310798+00:00
[2025-06-25 09:24:48,473] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:48,474] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:48.474020+00:00
[2025-06-25 09:24:59,281] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:24:59,282] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:49:59.282142+00:00
[2025-06-25 09:25:08,316] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.32s           2025-06-25T03:54:53
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.75s           2025-06-25T03:54:55
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.24s           2025-06-25T03:54:53
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.79s           2025-06-25T03:54:57
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.52s           2025-06-25T03:54:47
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.82s           2025-06-25T03:54:46
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.80s           2025-06-25T03:54:45
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.71s           2025-06-25T03:54:50
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.75s           2025-06-25T03:54:48
================================================================================
[2025-06-25 09:25:09,318] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:09,318] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:09.318801+00:00
[2025-06-25 09:25:19,357] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:19,357] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:19.357797+00:00
[2025-06-25 09:25:29,678] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:29,679] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:29.679419+00:00
[2025-06-25 09:25:39,159] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.50s           2025-06-25T03:55:26
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.75s           2025-06-25T03:55:28
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.21s           2025-06-25T03:55:26
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.79s           2025-06-25T03:55:30
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.11s           2025-06-25T03:55:20
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.85s           2025-06-25T03:55:18
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.69s           2025-06-25T03:55:17
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.64s           2025-06-25T03:55:22
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.75s           2025-06-25T03:55:20
================================================================================
[2025-06-25 09:25:40,161] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:40,161] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:40.161562+00:00
[2025-06-25 09:25:50,214] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:25:50,215] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:50:50.215078+00:00
[2025-06-25 09:26:00,632] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:00,632] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:00.632480+00:00
[2025-06-25 09:26:09,395] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.34s           2025-06-25T03:55:59
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.75s           2025-06-25T03:56:01
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  3.27s           2025-06-25T03:55:59
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.79s           2025-06-25T03:56:03
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.26s           2025-06-25T03:55:52
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.73s           2025-06-25T03:55:50
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.80s           2025-06-25T03:55:48
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.80s           2025-06-25T03:55:54
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.75s           2025-06-25T03:55:52
================================================================================
[2025-06-25 09:26:11,399] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:11,399] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:11.399747+00:00
[2025-06-25 09:26:21,462] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:21,462] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:21.462496+00:00
[2025-06-25 09:26:31,669] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:31,669] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:31.669849+00:00
[2025-06-25 09:26:39,496] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.26s           2025-06-25T03:56:31
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.73s           2025-06-25T03:56:33
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.20s           2025-06-25T03:56:32
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.84s           2025-06-25T03:56:36
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.18s           2025-06-25T03:56:25
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.75s           2025-06-25T03:56:22
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.66s           2025-06-25T03:56:21
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.89s           2025-06-25T03:56:26
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.92s           2025-06-25T03:56:24
================================================================================
[2025-06-25 09:26:42,256] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:42,256] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:42.256429+00:00
[2025-06-25 09:26:52,292] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:26:52,292] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:51:52.292770+00:00
[2025-06-25 09:27:02,797] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:02,798] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:02.798174+00:00
[2025-06-25 09:27:09,601] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.39s           2025-06-25T03:57:05
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.69s           2025-06-25T03:57:06
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.42s           2025-06-25T03:57:05
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.71s           2025-06-25T03:57:09
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.26s           2025-06-25T03:56:57
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.74s           2025-06-25T03:56:54
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.72s           2025-06-25T03:56:53
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.68s           2025-06-25T03:56:58
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.68s           2025-06-25T03:56:57
================================================================================
[2025-06-25 09:27:13,243] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:13,243] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:13.243256+00:00
[2025-06-25 09:27:23,274] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:23,275] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:23.275164+00:00
[2025-06-25 09:27:33,409] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:33,409] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:33.409495+00:00
[2025-06-25 09:27:39,866] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.35s           2025-06-25T03:57:37
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.66s           2025-06-25T03:57:39
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.42s           2025-06-25T03:57:37
/opt/airflow/dags/monitoring/trading_checks_builder.py            5014  0.01s             1           0  2.71s           2025-06-25T03:57:09
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  3.20s           2025-06-25T03:57:31
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.81s           2025-06-25T03:57:26
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.80s           2025-06-25T03:57:25
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.01s           2025-06-25T03:57:31
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.76s           2025-06-25T03:57:29
================================================================================
[2025-06-25 09:27:43,440] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:43,441] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:43.441124+00:00
[2025-06-25 09:27:53,476] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:27:53,477] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:52:53.476992+00:00
[2025-06-25 09:28:03,548] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:03,549] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:03.549037+00:00
[2025-06-25 09:28:07,373] {manager.py:653} INFO - Searching for files in /opt/airflow/dags
[2025-06-25 09:28:07,377] {manager.py:656} INFO - There are 9 files in /opt/airflow/dags
[2025-06-25 09:28:10,403] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py          5346  2.01s             1           0  2.35s           2025-06-25T03:57:37
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.66s           2025-06-25T03:57:39
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py   5349  2.01s             1           0  2.42s           2025-06-25T03:57:37
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.70s           2025-06-25T03:57:42
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.33s           2025-06-25T03:58:03
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.81s           2025-06-25T03:57:59
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.94s           2025-06-25T03:57:57
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.83s           2025-06-25T03:58:05
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  2.95s           2025-06-25T03:58:02
================================================================================
[2025-06-25 09:28:13,741] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:13,741] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:13.741532+00:00
[2025-06-25 09:28:24,283] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:24,283] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:24.283675+00:00
[2025-06-25 09:28:34,517] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:34,518] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:34.518278+00:00
[2025-06-25 09:28:40,973] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py          5875  0.01s             1           0  2.34s           2025-06-25T03:58:10
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.59s           2025-06-25T03:58:12
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py   5878  0.00s             1           0  2.28s           2025-06-25T03:58:10
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.73s           2025-06-25T03:58:15
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.29s           2025-06-25T03:58:36
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.60s           2025-06-25T03:58:31
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.71s           2025-06-25T03:58:29
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.70s           2025-06-25T03:58:37
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.85s           2025-06-25T03:58:34
================================================================================
[2025-06-25 09:28:44,965] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:44,965] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:44.965642+00:00
[2025-06-25 09:28:55,285] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:28:55,285] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:53:55.285436+00:00
[2025-06-25 09:29:05,485] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:05,486] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:05.486229+00:00
[2025-06-25 09:29:11,248] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.25s           2025-06-25T03:58:43
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.75s           2025-06-25T03:58:44
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.30s           2025-06-25T03:58:43
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.67s           2025-06-25T03:58:48
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.26s           2025-06-25T03:59:08
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.81s           2025-06-25T03:59:03
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.73s           2025-06-25T03:59:01
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.74s           2025-06-25T03:59:09
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  2.00s           2025-06-25T03:59:07
================================================================================
[2025-06-25 09:29:15,574] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:15,574] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:15.574331+00:00
[2025-06-25 09:29:25,763] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:25,763] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:25.763918+00:00
[2025-06-25 09:29:35,986] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:35,987] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:35.986992+00:00
[2025-06-25 09:29:41,398] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.32s           2025-06-25T03:59:15
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.64s           2025-06-25T03:59:17
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.09s           2025-06-25T03:59:15
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.89s           2025-06-25T03:59:21
/opt/airflow/dags/ops/dead_strat_exit_builder.py                  6810  2.39s             1           0  2.26s           2025-06-25T03:59:08
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.91s           2025-06-25T03:59:35
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.73s           2025-06-25T03:59:32
/opt/airflow/dags/ops/oms_db_copy_builder.py                      6855  2.01s             1           0  1.74s           2025-06-25T03:59:09
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.68s           2025-06-25T03:59:39
================================================================================
[2025-06-25 09:29:46,630] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:46,630] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:46.630627+00:00
[2025-06-25 09:29:57,224] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:29:57,224] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:54:57.224430+00:00
[2025-06-25 09:30:07,501] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:07,501] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:07.501382+00:00
[2025-06-25 09:30:11,432] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.20s           2025-06-25T03:59:47
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.64s           2025-06-25T03:59:49
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.26s           2025-06-25T03:59:48
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.97s           2025-06-25T03:59:55
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.61s           2025-06-25T03:59:41
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.67s           2025-06-25T04:00:07
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.92s           2025-06-25T04:00:05
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.59s           2025-06-25T03:59:41
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.91s           2025-06-25T04:00:11
================================================================================
[2025-06-25 09:30:18,295] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:18,295] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:18.295708+00:00
[2025-06-25 09:30:28,837] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:28,837] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:28.837796+00:00
[2025-06-25 09:30:39,174] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:39,174] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:39.174429+00:00
[2025-06-25 09:30:41,663] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.30s           2025-06-25T04:00:20
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.69s           2025-06-25T04:00:22
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.51s           2025-06-25T04:00:20
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.84s           2025-06-25T04:00:28
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.62s           2025-06-25T04:00:14
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.92s           2025-06-25T04:00:40
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.78s           2025-06-25T04:00:37
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.85s           2025-06-25T04:00:14
/opt/airflow/dags/ops/oms_latency_check_builder.py                7825  0.01s             1           0  1.91s           2025-06-25T04:00:11
================================================================================
[2025-06-25 09:30:49,346] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:49,347] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:49.347322+00:00
[2025-06-25 09:30:59,658] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:30:59,659] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:55:59.658997+00:00
[2025-06-25 09:31:10,184] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:10,184] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:10.184638+00:00
[2025-06-25 09:31:11,880] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.28s           2025-06-25T04:00:53
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.67s           2025-06-25T04:00:55
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.20s           2025-06-25T04:00:53
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.92s           2025-06-25T04:01:01
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.43s           2025-06-25T04:00:47
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.68s           2025-06-25T04:01:11
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.71s           2025-06-25T04:01:09
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.74s           2025-06-25T04:00:47
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.67s           2025-06-25T04:00:43
================================================================================
[2025-06-25 09:31:21,088] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:21,088] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:21.088379+00:00
[2025-06-25 09:31:31,112] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:31,112] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:31.112465+00:00
[2025-06-25 09:31:41,151] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:41,152] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:41.152075+00:00
[2025-06-25 09:31:42,118] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.43s           2025-06-25T04:01:26
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.64s           2025-06-25T04:01:28
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.35s           2025-06-25T04:01:26
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.70s           2025-06-25T04:01:34
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.19s           2025-06-25T04:01:20
/opt/airflow/dags/ops/oms_rollover_builder.py                     8838  0.00s             1           0  1.68s           2025-06-25T04:01:11
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.97s           2025-06-25T04:01:42
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.94s           2025-06-25T04:01:19
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.72s           2025-06-25T04:01:15
================================================================================
[2025-06-25 09:31:51,845] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:31:51,845] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:56:51.845528+00:00
[2025-06-25 09:32:02,294] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:02,295] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:02.295291+00:00
[2025-06-25 09:32:12,153] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.31s           2025-06-25T04:01:59
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.83s           2025-06-25T04:02:01
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.49s           2025-06-25T04:01:59
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  3.83s           2025-06-25T04:02:08
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.15s           2025-06-25T04:01:52
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.94s           2025-06-25T04:01:44
/opt/airflow/dags/ops/one_second_exit_application_builder.py      9322  0.01s             1           0  1.97s           2025-06-25T04:01:42
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.71s           2025-06-25T04:01:50
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.85s           2025-06-25T04:01:47
================================================================================
[2025-06-25 09:32:12,669] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:12,670] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:12.670004+00:00
[2025-06-25 09:32:23,071] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:23,071] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:23.071768+00:00
[2025-06-25 09:32:33,529] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:33,529] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:33.529426+00:00
[2025-06-25 09:32:42,241] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.29s           2025-06-25T04:02:31
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.64s           2025-06-25T04:02:33
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.23s           2025-06-25T04:02:31
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.69s           2025-06-25T04:02:41
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.18s           2025-06-25T04:02:24
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.64s           2025-06-25T04:02:16
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.75s           2025-06-25T04:02:13
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.72s           2025-06-25T04:02:22
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.73s           2025-06-25T04:02:19
================================================================================
[2025-06-25 09:32:43,971] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:43,972] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:43.972045+00:00
[2025-06-25 09:32:54,067] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:32:54,068] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:57:54.068258+00:00
[2025-06-25 09:33:04,112] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:33:04,113] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:58:04.113002+00:00
[2025-06-25 09:33:07,922] {manager.py:653} INFO - Searching for files in /opt/airflow/dags
[2025-06-25 09:33:07,926] {manager.py:656} INFO - There are 10 files in /opt/airflow/dags
[2025-06-25 09:33:12,964] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.26s           2025-06-25T04:03:05
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.84s           2025-06-25T04:03:05
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.21s           2025-06-25T04:03:04
/opt/airflow/dags/monitoring/trading_checks_builder.py           10380  1.01s             1           0  2.69s           2025-06-25T04:02:41
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.37s           2025-06-25T04:02:57
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  2.73s           2025-06-25T04:02:49
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.80s           2025-06-25T04:02:45
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.73s           2025-06-25T04:02:54
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.80s           2025-06-25T04:02:51
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  3.03s           2025-06-25T04:03:10
================================================================================
[2025-06-25 09:33:14,920] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:33:14,921] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:58:14.921141+00:00
[2025-06-25 09:33:24,993] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:33:24,994] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:58:24.994137+00:00
[2025-06-25 09:33:35,173] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:33:35,173] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:58:35.173509+00:00
[2025-06-25 09:33:43,217] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.30s           2025-06-25T04:03:37
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.70s           2025-06-25T04:03:38
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.34s           2025-06-25T04:03:36
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.97s           2025-06-25T04:03:14
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.14s           2025-06-25T04:03:30
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.70s           2025-06-25T04:03:21
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.70s           2025-06-25T04:03:17
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  3.81s           2025-06-25T04:03:28
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.65s           2025-06-25T04:03:22
/opt/airflow/dags/trading/balte_trading_builder.py               10909  2.01s             1           0  3.03s           2025-06-25T04:03:10
================================================================================
[2025-06-25 09:33:45,221] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:33:45,221] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:58:45.221820+00:00
[2025-06-25 09:33:55,261] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:33:55,261] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:58:55.261673+00:00
[2025-06-25 09:34:05,434] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:34:05,435] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:59:05.435063+00:00
[2025-06-25 09:34:14,005] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.31s           2025-06-25T04:04:10
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.95s           2025-06-25T04:04:10
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.16s           2025-06-25T04:04:09
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.80s           2025-06-25T04:03:48
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.58s           2025-06-25T04:04:02
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.74s           2025-06-25T04:03:53
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.74s           2025-06-25T04:03:49
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  3.15s           2025-06-25T04:04:02
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  2.04s           2025-06-25T04:03:55
/opt/airflow/dags/trading/balte_trading_builder.py               11514  0.01s             1           0  2.29s           2025-06-25T04:03:43
================================================================================
[2025-06-25 09:34:16,010] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:34:16,010] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:59:16.010326+00:00
[2025-06-25 09:34:26,028] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:34:26,029] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:59:26.029162+00:00
[2025-06-25 09:34:36,072] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:34:36,072] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:59:36.072955+00:00
[2025-06-25 09:34:44,212] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.29s           2025-06-25T04:04:42
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.69s           2025-06-25T04:04:43
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.42s           2025-06-25T04:04:41
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.99s           2025-06-25T04:04:21
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.19s           2025-06-25T04:04:35
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.73s           2025-06-25T04:04:25
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.75s           2025-06-25T04:04:21
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.97s           2025-06-25T04:04:34
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.67s           2025-06-25T04:04:27
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.29s           2025-06-25T04:04:16
================================================================================
[2025-06-25 09:34:46,216] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:34:46,216] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:59:46.216576+00:00
[2025-06-25 09:34:56,321] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:34:56,321] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 03:59:56.321738+00:00
[2025-06-25 09:35:06,357] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:35:06,357] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:00:06.357565+00:00
[2025-06-25 09:35:14,845] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py         12570  2.12s             1           0  2.29s           2025-06-25T04:04:42
/opt/airflow/dags/monitoring/restrictions_alert_builder.py       12646  0.92s             1           0  1.69s           2025-06-25T04:04:43
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.20s           2025-06-25T04:05:13
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.73s           2025-06-25T04:04:54
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.36s           2025-06-25T04:05:07
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.83s           2025-06-25T04:04:57
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.74s           2025-06-25T04:04:54
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.91s           2025-06-25T04:05:07
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.76s           2025-06-25T04:04:59
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.11s           2025-06-25T04:04:48
================================================================================
[2025-06-25 09:35:16,703] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:35:16,703] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:00:16.703881+00:00
[2025-06-25 09:35:26,925] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:35:26,925] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:00:26.925842+00:00
[2025-06-25 09:35:37,102] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:35:37,103] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:00:37.103148+00:00
[2025-06-25 09:35:45,357] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py         13175  0.00s             1           0  2.41s           2025-06-25T04:05:15
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  2.78s           2025-06-25T04:05:16
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py  13132  1.01s             1           0  2.20s           2025-06-25T04:05:13
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.74s           2025-06-25T04:05:27
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.22s           2025-06-25T04:05:40
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  2.17s           2025-06-25T04:05:29
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.75s           2025-06-25T04:05:26
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.71s           2025-06-25T04:05:39
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.60s           2025-06-25T04:05:32
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.20s           2025-06-25T04:05:20
================================================================================
[2025-06-25 09:35:47,362] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:35:47,362] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:00:47.362367+00:00
[2025-06-25 09:35:57,560] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:35:57,560] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:00:57.560968+00:00
[2025-06-25 09:36:08,077] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:36:08,077] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:01:08.077567+00:00
[2025-06-25 09:36:15,486] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  3.41s           2025-06-25T04:05:48
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.85s           2025-06-25T04:05:49
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.23s           2025-06-25T04:05:46
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.76s           2025-06-25T04:06:00
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.38s           2025-06-25T04:06:13
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.83s           2025-06-25T04:06:02
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.66s           2025-06-25T04:05:58
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.80s           2025-06-25T04:06:11
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.76s           2025-06-25T04:06:04
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.31s           2025-06-25T04:05:53
================================================================================
[2025-06-25 09:36:18,139] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:36:18,139] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:01:18.139373+00:00
[2025-06-25 09:36:28,687] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:36:28,687] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:01:28.687710+00:00
[2025-06-25 09:36:38,892] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:36:38,892] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:01:38.892654+00:00
[2025-06-25 09:36:45,632] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.52s           2025-06-25T04:06:21
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.65s           2025-06-25T04:06:21
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.46s           2025-06-25T04:06:19
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.69s           2025-06-25T04:06:33
/opt/airflow/dags/ops/dead_strat_exit_builder.py                 14266  2.01s             1           0  2.38s           2025-06-25T04:06:13
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.77s           2025-06-25T04:06:34
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.87s           2025-06-25T04:06:30
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.71s           2025-06-25T04:06:43
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.62s           2025-06-25T04:06:35
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.19s           2025-06-25T04:06:25
================================================================================
[2025-06-25 09:36:49,073] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:36:49,073] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:01:49.073818+00:00
[2025-06-25 09:36:59,121] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:36:59,121] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:01:59.121750+00:00
[2025-06-25 09:37:09,328] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:37:09,328] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:02:09.328771+00:00
[2025-06-25 09:37:16,365] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.12s           2025-06-25T04:06:53
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.71s           2025-06-25T04:06:53
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.07s           2025-06-25T04:06:51
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  3.01s           2025-06-25T04:07:06
/opt/airflow/dags/ops/dead_strat_exit_builder.py                 14871  0.00s             1           0  2.45s           2025-06-25T04:06:46
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.86s           2025-06-25T04:07:06
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.78s           2025-06-25T04:07:02
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.78s           2025-06-25T04:07:15
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.81s           2025-06-25T04:07:08
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.28s           2025-06-25T04:06:58
================================================================================
[2025-06-25 09:37:19,459] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:37:19,459] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:02:19.459586+00:00
[2025-06-25 09:37:29,637] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:37:29,637] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:02:29.637799+00:00
[2025-06-25 09:37:39,650] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:37:39,651] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:02:39.651281+00:00
[2025-06-25 09:37:46,412] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.17s           2025-06-25T04:07:27
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.99s           2025-06-25T04:07:25
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  3.19s           2025-06-25T04:07:25
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.82s           2025-06-25T04:07:39
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.25s           2025-06-25T04:07:18
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  2.00s           2025-06-25T04:07:38
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  2.72s           2025-06-25T04:07:35
/opt/airflow/dags/ops/oms_db_copy_builder.py                     15431  0.88s             1           0  1.78s           2025-06-25T04:07:15
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.69s           2025-06-25T04:07:40
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.18s           2025-06-25T04:07:30
================================================================================
[2025-06-25 09:37:50,202] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:37:50,202] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:02:50.202863+00:00
[2025-06-25 09:38:00,545] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:38:00,546] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:03:00.546392+00:00
[2025-06-25 09:38:08,765] {manager.py:653} INFO - Searching for files in /opt/airflow/dags
[2025-06-25 09:38:08,768] {manager.py:656} INFO - There are 10 files in /opt/airflow/dags
[2025-06-25 09:38:10,795] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:38:10,795] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:03:10.795345+00:00
[2025-06-25 09:38:16,551] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.23s           2025-06-25T04:08:00
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  2.94s           2025-06-25T04:07:59
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.30s           2025-06-25T04:07:58
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.99s           2025-06-25T04:08:12
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.19s           2025-06-25T04:07:51
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.99s           2025-06-25T04:08:11
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.98s           2025-06-25T04:08:07
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.66s           2025-06-25T04:07:47
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.77s           2025-06-25T04:08:13
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.24s           2025-06-25T04:08:03
================================================================================
[2025-06-25 09:38:20,832] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:38:20,833] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:03:20.833044+00:00
[2025-06-25 09:38:30,883] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:38:30,883] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:03:30.883782+00:00
[2025-06-25 09:38:41,255] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:38:41,256] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:03:41.256322+00:00
[2025-06-25 09:38:46,858] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                        PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.35s           2025-06-25T04:08:33
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.76s           2025-06-25T04:08:31
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.27s           2025-06-25T04:08:31
/opt/airflow/dags/monitoring/trading_checks_builder.py                                    1           0  2.79s           2025-06-25T04:08:46
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.11s           2025-06-25T04:08:23
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  1.75s           2025-06-25T04:08:44
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.75s           2025-06-25T04:08:40
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  1.65s           2025-06-25T04:08:19
/opt/airflow/dags/ops/oms_latency_check_builder.py                                        1           0  1.84s           2025-06-25T04:08:45
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.36s           2025-06-25T04:08:36
================================================================================
[2025-06-25 09:38:51,874] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:38:51,875] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:03:51.875131+00:00
[2025-06-25 09:39:02,104] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:39:02,105] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:04:02.105275+00:00
[2025-06-25 09:39:12,218] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:39:12,219] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:04:12.219043+00:00
[2025-06-25 09:39:17,255] {manager.py:774} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                          PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
---------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/monitoring/trading_slippage_builder.py                                  1           0  2.21s           2025-06-25T04:09:06
/opt/airflow/dags/monitoring/restrictions_alert_builder.py                                1           0  1.95s           2025-06-25T04:09:04
/opt/airflow/dags/monitoring/trading_pickle_db_check_builder.py                           1           0  2.58s           2025-06-25T04:09:03
/opt/airflow/dags/monitoring/trading_checks_builder.py           17128  0.69s             1           0  2.79s           2025-06-25T04:08:46
/opt/airflow/dags/ops/dead_strat_exit_builder.py                                          1           0  2.18s           2025-06-25T04:08:56
/opt/airflow/dags/ops/oms_rollover_builder.py                                             1           0  2.33s           2025-06-25T04:09:16
/opt/airflow/dags/ops/one_second_exit_application_builder.py                              1           0  1.73s           2025-06-25T04:09:12
/opt/airflow/dags/ops/oms_db_copy_builder.py                                              1           0  2.92s           2025-06-25T04:08:52
/opt/airflow/dags/ops/oms_latency_check_builder.py               17123  1.01s             1           0  1.84s           2025-06-25T04:08:45
/opt/airflow/dags/trading/balte_trading_builder.py                                        1           0  2.35s           2025-06-25T04:09:09
================================================================================
[2025-06-25 09:39:22,243] {manager.py:1051} INFO - Finding 'running' jobs without a recent heartbeat
[2025-06-25 09:39:22,243] {manager.py:1055} INFO - Failing jobs without heartbeat after 2025-06-25 04:04:22.243843+00:00
