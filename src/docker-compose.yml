name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}
networks:
  custom_network:
    driver: bridge
    name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}
services:
  airflow-scheduler:
    command:
    - "chmod +x /opt/entrypoint.sh && \n/opt/entrypoint.sh && \nexec /opt/conda/envs/${CONDA_ENV_NAME}/bin/airflow\
      \ scheduler\n"
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_airflow_scheduler
    depends_on:
    - postgres
    entrypoint:
    - /bin/sh
    - -c
    env_file:
    - .env
    extra_hosts:
    - host.docker.internal:host-gateway
    image: ${CONTAINER_REGISTRY_BASE}/${BALTE_CONTAINER_IMAGE_PATH}
    networks:
    - custom_network
    restart: always
    volumes:
    - ./:/app/src:rw
    - ./config/airflow:/root/airflow:rw
    - ./exchange/dags:/opt/airflow/dags:rw
    - ../data/logs/airflow/:/opt/airflow/logs:rw
    - ./config/airflow/entrypoint.sh:/opt/entrypoint.sh
    - ../data/logs:/opt/balte_live/log:rw
    - ${LOCAL_PYCE_TOKEN_PATH}:/opt/balte_live/pyce_token:rw
    - ./localtime:/etc/localtime:ro
  airflow-webserver:
    command:
    - /opt/conda/envs/${CONDA_ENV_NAME}/bin/airflow
    - webserver
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_airflow_webserver
    depends_on:
    - postgres
    env_file:
    - .env
    extra_hosts:
    - host.docker.internal:host-gateway
    image: ${CONTAINER_REGISTRY_BASE}/${BALTE_CONTAINER_IMAGE_PATH}
    networks:
    - custom_network
    ports:
    - ${AIRFLOW_EXPOSED_PORT}:${AIRFLOW_PORT}
    restart: always
    volumes:
    - ./:/app/src:rw
    - ./config/airflow:/root/airflow:rw
    - ./exchange/dags:/opt/airflow/dags:rw
    - ../data/logs/airflow/:/opt/airflow/logs:rw
    - ./localtime:/etc/localtime:ro
  alerts:
    command:
    - "sleep 30 && # false positives in the beginning as containers restart for example\
      \ because ib-gateway is up but not logged in etc\ndocker events --filter event=stop\
      \ --filter event=die --filter label=\"com.docker.compose.project\"=$COMPOSE_PROJECT_NAME\
      \ --format \\\n  \"{{.TimeNano}} {{.Type}} {{.Action}} {{.Actor.Attributes.name}}\
      \ (id = {{.ID}})\" | \\\n  while read line; do python3 /app/src/core/apps/send_alert.py\
      \ \"$$line\"; done\n"
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_alerts
    entrypoint:
    - /bin/sh
    - -c
    env_file:
    - .env
    image: ${CONTAINER_REGISTRY_BASE}/docker_alerts:latest
    networks:
    - custom_network
    restart: always
    volumes:
    - ./:/app/src:ro
    - /var/run/docker.sock:/var/run/docker.sock:ro
    - ./localtime:/etc/localtime:ro
  alloy:
    command:
    - run
    - --server.http.listen-addr=0.0.0.0:12345
    - --storage.path=/var/lib/alloy/data
    - /etc/alloy/config.alloy
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_alloy
    image: grafana/alloy:v1.7.5
    networks:
    - custom_network
    ports:
    - ${ALLOY_EXPOSED_PORT}:12345
    restart: unless-stopped
    volumes:
    - ./config/alloy/config.alloy:/etc/alloy/config.alloy
    - ../data/alloy:/var/lib/alloy/data
    - ../data/logs:/opt/logs:ro
    - ./localtime:/etc/localtime:ro
  cron:
    command: sh -c "cat /root/cron.txt > /etc/crontabs/root && crond -f -d 8"
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_cron
    env_file:
    - .env
    image: docker:cli
    networks:
    - custom_network
    restart: unless-stopped
    volumes:
    - ./config/cron.txt:/root/cron.txt
    - /var/run/docker.sock:/var/run/docker.sock:ro
    - ./localtime:/etc/localtime:ro
  db:
    command: --default-authentication-plugin=mysql_native_password
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_mysql
    env_file:
    - .env
    healthcheck:
      interval: 30s
      retries: 10
      test:
      - CMD
      - /usr/bin/mysql
      - --user=${MYSQL_USER}
      - --password=${MYSQL_PASSWORD}
      - --execute
      - SHOW DATABASES;
      timeout: 5s
    image: mysql:5.7
    networks:
    - custom_network
    ports:
    - ${MYSQL_EXPOSED_PORT}:${MYSQL_PORT}
    restart: always
    volumes:
    - ./config/mysql-master/:/etc/mysql/conf.d/:rw
    - ./config/mysql-initdb:/docker-entrypoint-initdb.d:rw
    - ../data/mysql:/var/lib/mysql
    - ./localtime:/etc/localtime:ro
  exit_portal:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_exit_portal
    depends_on:
      db:
        condition: service_healthy
      minio:
        condition: service_healthy
    entrypoint:
    - /bin/sh
    - -c
    - chmod +x /opt/entrypoint.sh && /opt/entrypoint.sh
    env_file:
    - .env
    healthcheck:
      interval: 30s
      retries: 3
      test:
      - CMD
      - curl
      - -f
      - http://localhost:${EXIT_PORTAL_PORT}/_stcore/health
      timeout: 20s
    hostname: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_exit_portal
    image: ${CONTAINER_REGISTRY_BASE}/${BALTE_CONTAINER_IMAGE_PATH}
    networks:
    - custom_network
    ports:
    - ${EXIT_PORTAL_EXPOSED_PORT}:${EXIT_PORTAL_PORT}
    restart: always
    volumes:
    - .:/app/src:rw
    - ./localtime:/etc/localtime:ro
    - ./config/streamlit/entrypoint.sh:/opt/entrypoint.sh
    - ./config/streamlit/auth.yaml:/opt/auth.yaml
    - ../data/logs/exit_portal:/opt/exit_portal/log:rw
  kafka:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_kafka
    env_file:
    - .env
    healthcheck:
      interval: 10s
      retries: 5
      start_period: 10s
      test:
      - CMD
      - kafka-topics.sh
      - --bootstrap-server
      - localhost:${KAFKA_PLAINTEXT_PORT}
      - --list
      timeout: 5s
    image: bitnami/kafka:3.7
    networks:
    - custom_network
    ports:
    - ${KAFKA_PLAINTEXT_PORT}:${KAFKA_PLAINTEXT_PORT}
    restart: always
    volumes:
    - ../data/kafka/:/bitnami/kafka
    - ./localtime:/etc/localtime:ro
  kafka-ui:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_kafka_ui
    depends_on:
    - kafka
    env_file:
    - .env
    image: provectuslabs/kafka-ui:latest
    networks:
    - custom_network
    ports:
    - ${KAFKA_UI_EXPOSED_PORT}:${KAFKA_UI_PORT}
    profiles:
    - frontend
    restart: always
    volumes:
    - ./localtime:/etc/localtime:ro
  live_bot:
    command:
    - /opt/conda/envs/${CONDA_ENV_NAME}/bin/python
    - /app/src/exchange/apps/live_bot.py
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_live_bot
    env_file:
    - .env
    hostname: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_live_bot
    image: ${CONTAINER_REGISTRY_BASE}/${BALTE_CONTAINER_IMAGE_PATH}
    networks:
    - custom_network
    restart: always
    volumes:
    - .:/app/src:ro
    - ./localtime:/etc/localtime:ro
  minio:
    container_name: ${EXCHANGE_TYPE}-${EXECUTION_LEVEL}-minio
    entrypoint:
    - /bin/sh
    - -c
    - chmod +x /opt/entrypoint.sh && /opt/entrypoint.sh
    env_file:
    - .env
    healthcheck:
      interval: 30s
      retries: 3
      test:
      - CMD
      - curl
      - -f
      - http://localhost:${MINIO_API_PORT}/minio/health/live
      timeout: 20s
    hostname: ${EXCHANGE_TYPE}-${EXECUTION_LEVEL}-minio
    image: quay.io/minio/minio:RELEASE.2023-09-30T07-02-29Z
    networks:
    - custom_network
    ports:
    - ${MINIO_CONSOLE_EXPOSED_PORT}:${MINIO_CONSOLE_PORT}
    - ${MINIO_API_EXPOSED_PORT}:${MINIO_API_PORT}
    restart: always
    volumes:
    - ../data/minio/data:/data
    - ./config/minio/lifecycle.json:/opt/lifecycle.json
    - ./config/minio/entrypoint.sh:/opt/entrypoint.sh
    - ./localtime:/etc/localtime:ro
  oms:
    command:
    - /opt/conda/envs/env_oms_0.0.3/bin/python
    - runner.py
    - ${EXECUTION_LEVEL}
    - --trading_universe
    - ${EXCHANGE_TYPE}
    - --port
    - ${OMS_PORT}
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_oms
    depends_on:
      db:
        condition: service_healthy
      kafka:
        condition: service_started
    env_file:
    - .env
    image: ${CONTAINER_REGISTRY_BASE}/${OMS_CONTAINER_IMAGE_PATH}
    networks:
    - custom_network
    ports:
    - ${OMS_EXPOSED_PORT}:${OMS_PORT}
    restart: always
    volumes:
    - ../data/logs:/opt/balte_live/log:rw
    - ./localtime:/etc/localtime:ro
  oms_db_executor:
    command:
    - /opt/conda/envs/env_oms_0.0.3/bin/python
    - db_executor_runner.py
    - ${EXECUTION_LEVEL}
    - ${EXCHANGE_TYPE}
    - sync
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_oms_db_executor
    depends_on:
    - kafka
    env_file:
    - .env
    image: ${CONTAINER_REGISTRY_BASE}/${OMS_CONTAINER_IMAGE_PATH}
    networks:
    - custom_network
    restart: always
    volumes:
    - ../data/logs/oms_db_executor:/opt/balte_live/log/oms_db_executor:rw
    - ./localtime:/etc/localtime:ro
  postgres:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_postgres
    env_file:
    - .env
    extra_hosts:
    - host.docker.internal:host-gateway
    healthcheck:
      interval: 5s
      retries: 5
      test:
      - CMD
      - pg_isready
      - -U
      - airflow
    hostname: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_postgres
    image: postgres:11
    networks:
    - custom_network
    ports:
    - ${POSTGRES_EXPOSED_PORT}:${POSTGRES_PORT}
    restart: always
    volumes:
    - ../data/postgresql:/var/lib/postgresql/data:rw
    - ./localtime:/etc/localtime:ro
