from core.dags.monitoring.position_mismatch_builder_base import (
    PositionMismatchDagBuilderBase,
)


class PositionMismatchDagBuilder(PositionMismatchDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """


position_mismatch_dag_builder = PositionMismatchDagBuilder(
    dag_id="position_mismatch_monitoring",
    schedule_interval="57 23 * * 1-5",
)
dag = position_mismatch_dag_builder.build_dag()
