from core.dags.monitoring.strat_wise_time_delta_builder_base import (
    StratWiseTimeDeltaBuilderBase,
)


class StratWiseTimeDeltaBuilder(StratWiseTimeDeltaBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )
        self.dag_default_args["email"] += [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]


strat_wise_time_delta_dag_builder = StratWiseTimeDeltaBuilder(
    dag_id="create_strat_wise_time_delta",
    schedule_interval="57 23 * * *",
)
dag = strat_wise_time_delta_dag_builder.build_dag()
