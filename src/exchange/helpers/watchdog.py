from core.helpers.watchdog_base import WatchdogBase
from core.helpers.utils import (
    get_live_strats,
    load_strats_pyce,
)
import logging
import pandas as pd
import balte.balte_config
from balte.utility_inbuilt import previous_date
import datetime
from minio import Minio  # type: ignore
from core.helpers.configstore import (
    MINIO_END_POINT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    EXCHANGE_TYPE,
)

minio_client = Minio(
    MINIO_END_POINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=False,
)
balte.utility_inbuilt.balte_initializer(EXCHANGE_TYPE.lower())


class watchdog(WatchdogBase):
    def __init__(self) -> None:
        super(watchdog, self).__init__()
        """
        Add data blocks that will be used for Watchdog
        """
        self.add_timestamps(continuous=("09:01", "23:55"), freq=1)
        self.data.optcom_onemin = self.add_data(
            "optcom_onemin", lookback_initialize=1, lookback_next=0
        )
        self.data.optcom = self.add_data(
            "optcom", lookback_initialize=1, lookback_next=0
        )
        self.data.mcx_spot_onemin = self.add_data(
            "mcx_spot_onemin", lookback_initialize=1, lookback_next=0
        )
        self.data.mcx_spot = self.add_data(
            "mcx_spot", lookback_initialize=1, lookback_next=0
        )
        self.data.mcx_fut_near_onemin = self.add_data(
            "mcx_fut_near_onemin", lookback_initialize=1, lookback_next=0
        )
        self.data.mcx_fut_near = self.add_data(
            "mcx_fut_near", lookback_initialize=1, lookback_next=0
        )
        # TODO: Add more 1440 universes

    def check_if_data_missing(self) -> None:
        today = (
            pd.Timestamp.now(tz=balte.balte_config.TIMEZONE)
            .replace(tzinfo=None)
            .normalize()
        )
        yesterday = previous_date(today)
        df = self.data.optcom_onemin["Close", "23:35"]
        if len(df) == 0:
            logging.error(f"OPTCOM_ONEMIN DATA MISSING for {yesterday}")
        df = self.data.optcom["Close", "23:35"]
        if len(df) == 0:
            logging.error(f"OPTCOM DATA MISSING for {yesterday}")

        df = self.data.mcx_spot_onemin["Close", "23:35"]
        if len(df) == 0:
            logging.error(f"MCX_SPOT_ONEMIN DATA MISSING for {yesterday}")
        df = self.data.mcx_spot["Close", "23:35"]
        if len(df) == 0:
            logging.error(f"MCX_SPOT DATA MISSING for {yesterday}")

        df = self.data.mcx_fut_near_onemin["Close", "23:35"]
        if len(df) == 0:
            logging.error(f"MCX_FUT_NEAR_ONEMIN DATA MISSING for {yesterday}")
        df = self.data.mcx_fut_near["Close", "23:35"]
        if len(df) == 0:
            logging.error(f"MCX_FUT_NEAR DATA MISSING for {yesterday}")

    def next(self, today: pd.Timestamp, timestamp: pd.Timestamp) -> None:
        time = timestamp.time().strftime("%H:%M")
        if time < "09:01" or time > "23:35":
            return
        df = self.data.optcom_onemin["Close", time]
        if len(df) == 0:
            logging.error(f"OPTCOM_ONEMIN DATA MISSING at {timestamp}")

        df = self.data.mcx_spot_onemin["Close", time]
        if len(df) == 0:
            logging.error(f"MCX_SPOT_ONEMIN DATA MISSING at {timestamp}")

        df = self.data.mcx_fut_near_onemin["Close", time]
        if len(df) == 0:
            logging.error(f"MCX_FUT_NEAR_ONEMIN DATA MISSING at {timestamp}")

        if int(time.split(":")[1]) % 5 == 0:
            df = self.data.optcom["Close", time]
            if len(df) == 0:
                logging.error(f"OPTCOM_ONEMIN DATA MISSING at {timestamp}")
            df = self.data.mcx_spot["Close", time]
            if len(df) == 0:
                logging.error(f"MCX_SPOT DATA MISSING at {timestamp}")
            df = self.data.mcx_fut_near["Close", time]
            if len(df) == 0:
                logging.error(f"MCX_FUT_NEAR DATA MISSING at {timestamp}")
        self._keep_only_30_min_bar_pickles_or_last_10()
