
loki.process "global_log_processor" {
  forward_to = [loki.write.primary.receiver,loki.write.secondary.receiver]

  stage.drop {
    expression = "^\\s*$"
  }

  stage.multiline {
    firstline     = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}[\\.,]\\d{3}"
    max_lines     = 0
    max_wait_time = "500ms"
  }

stage.regex {
  expression = "^(?P<timestamp>\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}[\\.,]\\d{3}) (?P<rest>.*)$"
}

stage.timestamp {
  source   = "timestamp"
  format   = "2006-01-02 15:04:05.000"
  fallback_formats = ["2006-01-02 15:04:05,000"]
  location = "Asia/Kolkata"
}

stage.output {
  source = "entry"
}
}










local.file_match "cbe" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/cbe/*.txt",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "cbe_logs_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "cbe" {
    targets    = local.file_match.cbe.targets
    forward_to = [loki.process.global_log_processor.receiver]
}



local.file_match "oms" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/oms/*.log",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "oms_logs_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "oms" {
    targets    = local.file_match.oms.targets
    forward_to = [loki.process.global_log_processor.receiver]
}



local.file_match "oms_db_executor" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/oms_db_executor/*.log",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "oms_db_exec_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "oms_db_executor" {
    targets    = local.file_match.oms_db_executor.targets
    forward_to = [loki.process.global_log_processor.receiver]
}



local.file_match "slave_logs" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/SLAVE/*.log",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "slave_logs_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "slave_logs" {
    targets    = local.file_match.slave_logs.targets
    forward_to = [loki.process.global_log_processor.receiver]
}



local.file_match "cluster_logs" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/CLUSTER/*.log",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "cluster_logs_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "cluster_logs" {
    targets    = local.file_match.cluster_logs.targets
    forward_to = [loki.process.global_log_processor.receiver]
}



local.file_match "watchdog_logs" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/WATCHDOG/*.log",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "watchdog_logs_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "watchdog_logs" {
    targets    = local.file_match.watchdog_logs.targets
    forward_to = [loki.process.global_log_processor.receiver]
}



local.file_match "exit_portal" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/exit_portal/*.log",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "exit_portal_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "exit_portal" {
    targets    = local.file_match.exit_portal.targets
    forward_to = [loki.process.global_log_processor.receiver]
}



local.file_match "exit_portal_strat" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/exit_strat_log/*.log",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "exit_portal_strat_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "exit_portal_strat" {
    targets    = local.file_match.exit_portal_strat.targets
    forward_to = [loki.process.global_log_processor.receiver]
}



local.file_match "one_second_exit_application_logs" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "/opt/logs/one_second_exit_application/one_second_exit_application.log.*",
        exchange      = "mcx_test",
        host          = "206_toyman",
        job           = "one_second_exit_application_mcx_test",
        handle_rename = true,
    }]
}

loki.source.file "one_second_exit_application_logs" {
    targets    = local.file_match.one_second_exit_application_logs.targets
    forward_to = [loki.process.global_log_processor.receiver]
}




loki.write "primary" {
    endpoint {
        url = "http://*************:3100/loki/api/v1/push"
    }
    external_labels = {}
}

loki.write "secondary" {
    endpoint {
        url = "http://*************:3100/loki/api/v1/push"
    }
    external_labels = {}
}