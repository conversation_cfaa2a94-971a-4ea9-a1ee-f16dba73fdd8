from abc import ABC, abstractmethod
from airflow.models import DAG
import pendulum
import datetime
from core.helpers.configstore import AIRFLOW_TIMEZONE
from core.helpers.alerting import task_fail_alert
from typing import Any, Dict
from balte.utility_inbuilt import balte_initializer
from core.helpers.configstore import EXCHANGE_TYPE


class DagBuilderBase(ABC):
    """
    Base Class that every DAG needs to inherit and implement
    """

    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__()
        try:
            balte_initializer(EXCHANGE_TYPE.lower())
        except Exception as e:
            # Log the error but don't fail DAG parsing
            # The balte_initializer will be called again during task execution
            print(f"Warning: balte_initializer failed during DAG parsing: {e}")
        self.dag_id = dag_id
        self.schedule_interval = schedule_interval
        self.dag_default_args: Dict[str, Any] = {
            "owner": "Airflow",
            "start_date": pendulum.datetime(
                year=2022, month=8, day=1, tz=AIRFLOW_TIMEZONE
            ),
            "depends_on_past": False,
            "email": ["<EMAIL>"],
            "email_on_retry": True,
            "retries": 0,
            "retry_delay": datetime.timedelta(minutes=5),
            "on_failure_callback": task_fail_alert,
        }
        self.dag_catchup = False

    @abstractmethod
    def build_dag(self, *args: Any, **kwargs: Any) -> DAG:
        """
        Every DAG needs to implement this function which should return the dag that needs to run
        """
        pass
